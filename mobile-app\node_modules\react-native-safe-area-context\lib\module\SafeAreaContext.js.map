{"version": 3, "names": ["React", "Dimensions", "StyleSheet", "NativeSafeAreaProvider", "isDev", "process", "env", "NODE_ENV", "SafeAreaInsetsContext", "createContext", "displayName", "SafeAreaFrameContext", "SafeAreaProvider", "children", "initialMetrics", "initialSafeAreaInsets", "style", "others", "parentInsets", "useParentSafeAreaInsets", "parentFrame", "useParentSafeAreaFrame", "insets", "setInsets", "useState", "frame", "set<PERSON>rame", "x", "y", "width", "get", "height", "onInsetsChange", "useCallback", "event", "nativeEvent", "next<PERSON><PERSON><PERSON>", "nextInsets", "curFrame", "curInsets", "bottom", "left", "right", "top", "createElement", "_extends", "styles", "fill", "Provider", "value", "SafeAreaListener", "onChange", "e", "create", "flex", "useContext", "NO_INSETS_ERROR", "useSafeAreaInsets", "Error", "useSafeAreaFrame", "withSafeAreaInsets", "WrappedComponent", "forwardRef", "props", "ref", "useSafeArea", "SafeAreaConsumer", "Consumer", "SafeAreaContext"], "sourceRoot": "../../src", "sources": ["SafeAreaContext.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAwB,cAAc;AACrE,SAASC,sBAAsB,QAAQ,0BAA0B;AAQjE,MAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AAEnD,OAAO,MAAMC,qBAAqB,gBAAGR,KAAK,CAACS,aAAa,CACtD,IACF,CAAC;AACD,IAAIL,KAAK,EAAE;EACTI,qBAAqB,CAACE,WAAW,GAAG,uBAAuB;AAC7D;AAEA,OAAO,MAAMC,oBAAoB,gBAAGX,KAAK,CAACS,aAAa,CAAc,IAAI,CAAC;AAC1E,IAAIL,KAAK,EAAE;EACTO,oBAAoB,CAACD,WAAW,GAAG,sBAAsB;AAC3D;AAWA,OAAO,SAASE,gBAAgBA,CAAC;EAC/BC,QAAQ;EACRC,cAAc;EACdC,qBAAqB;EACrBC,KAAK;EACL,GAAGC;AACkB,CAAC,EAAE;EACxB,MAAMC,YAAY,GAAGC,uBAAuB,CAAC,CAAC;EAC9C,MAAMC,WAAW,GAAGC,sBAAsB,CAAC,CAAC;EAC5C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CACxCV,cAAc,EAAEQ,MAAM,IAAIP,qBAAqB,IAAIG,YAAY,IAAI,IACrE,CAAC;EACD,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,KAAK,CAACwB,QAAQ,CACtCV,cAAc,EAAEW,KAAK,IACnBL,WAAW,IAAI;IACb;IACAO,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE5B,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,CAACD,KAAK;IACrCE,MAAM,EAAE9B,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,CAACC;EACnC,CACJ,CAAC;EACD,MAAMC,cAAc,GAAGhC,KAAK,CAACiC,WAAW,CAAEC,KAAwB,IAAK;IACrE,MAAM;MACJC,WAAW,EAAE;QAAEV,KAAK,EAAEW,SAAS;QAAEd,MAAM,EAAEe;MAAW;IACtD,CAAC,GAAGH,KAAK;IAETR,QAAQ,CAAEY,QAAQ,IAAK;MACrB;MACE;MACAF,SAAS,KACRA,SAAS,CAACL,MAAM,KAAKO,QAAQ,CAACP,MAAM,IACnCK,SAAS,CAACP,KAAK,KAAKS,QAAQ,CAACT,KAAK,IAClCO,SAAS,CAACT,CAAC,KAAKW,QAAQ,CAACX,CAAC,IAC1BS,SAAS,CAACR,CAAC,KAAKU,QAAQ,CAACV,CAAC,CAAC,EAC7B;QACA,OAAOQ,SAAS;MAClB,CAAC,MAAM;QACL,OAAOE,QAAQ;MACjB;IACF,CAAC,CAAC;IAEFf,SAAS,CAAEgB,SAAS,IAAK;MACvB,IACE,CAACA,SAAS,IACVF,UAAU,CAACG,MAAM,KAAKD,SAAS,CAACC,MAAM,IACtCH,UAAU,CAACI,IAAI,KAAKF,SAAS,CAACE,IAAI,IAClCJ,UAAU,CAACK,KAAK,KAAKH,SAAS,CAACG,KAAK,IACpCL,UAAU,CAACM,GAAG,KAAKJ,SAAS,CAACI,GAAG,EAChC;QACA,OAAON,UAAU;MACnB,CAAC,MAAM;QACL,OAAOE,SAAS;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvC,KAAA,CAAA4C,aAAA,CAACzC,sBAAsB,EAAA0C,QAAA;IACrB7B,KAAK,EAAE,CAAC8B,MAAM,CAACC,IAAI,EAAE/B,KAAK,CAAE;IAC5BgB,cAAc,EAAEA;EAAe,GAC3Bf,MAAM,GAETK,MAAM,IAAI,IAAI,gBACbtB,KAAA,CAAA4C,aAAA,CAACjC,oBAAoB,CAACqC,QAAQ;IAACC,KAAK,EAAExB;EAAM,gBAC1CzB,KAAA,CAAA4C,aAAA,CAACpC,qBAAqB,CAACwC,QAAQ;IAACC,KAAK,EAAE3B;EAAO,GAC3CT,QAC6B,CACH,CAAC,GAC9B,IACkB,CAAC;AAE7B;AAMA,OAAO,SAASqC,gBAAgBA,CAAC;EAC/BC,QAAQ;EACRnC,KAAK;EACLH,QAAQ;EACR,GAAGI;AACkB,CAAC,EAAE;EACxB,oBACEjB,KAAA,CAAA4C,aAAA,CAACzC,sBAAsB,EAAA0C,QAAA,KACjB5B,MAAM;IACVD,KAAK,EAAE,CAAC8B,MAAM,CAACC,IAAI,EAAE/B,KAAK,CAAE;IAC5BgB,cAAc,EAAGoB,CAAC,IAAK;MACrBD,QAAQ,CAAC;QACP7B,MAAM,EAAE8B,CAAC,CAACjB,WAAW,CAACb,MAAM;QAC5BG,KAAK,EAAE2B,CAAC,CAACjB,WAAW,CAACV;MACvB,CAAC,CAAC;IACJ;EAAE,IAEDZ,QACqB,CAAC;AAE7B;AAEA,MAAMiC,MAAM,GAAG5C,UAAU,CAACmD,MAAM,CAAC;EAC/BN,IAAI,EAAE;IAAEO,IAAI,EAAE;EAAE;AAClB,CAAC,CAAC;AAEF,SAASnC,uBAAuBA,CAAA,EAAsB;EACpD,OAAOnB,KAAK,CAACuD,UAAU,CAAC/C,qBAAqB,CAAC;AAChD;AAEA,SAASa,sBAAsBA,CAAA,EAAgB;EAC7C,OAAOrB,KAAK,CAACuD,UAAU,CAAC5C,oBAAoB,CAAC;AAC/C;AAEA,MAAM6C,eAAe,GACnB,wGAAwG;AAE1G,OAAO,SAASC,iBAAiBA,CAAA,EAAe;EAC9C,MAAMnC,MAAM,GAAGtB,KAAK,CAACuD,UAAU,CAAC/C,qBAAqB,CAAC;EACtD,IAAIc,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIoC,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAOlC,MAAM;AACf;AAEA,OAAO,SAASqC,gBAAgBA,CAAA,EAAS;EACvC,MAAMlC,KAAK,GAAGzB,KAAK,CAACuD,UAAU,CAAC5C,oBAAoB,CAAC;EACpD,IAAIc,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIiC,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAO/B,KAAK;AACd;AAMA,OAAO,SAASmC,kBAAkBA,CAChCC,gBAEC,EAGD;EACA,oBAAO7D,KAAK,CAAC8D,UAAU,CAAa,CAACC,KAAK,EAAEC,GAAG,KAAK;IAClD,MAAM1C,MAAM,GAAGmC,iBAAiB,CAAC,CAAC;IAClC,oBAAOzD,KAAA,CAAA4C,aAAA,CAACiB,gBAAgB,EAAAhB,QAAA,KAAKkB,KAAK;MAAEzC,MAAM,EAAEA,MAAO;MAAC0C,GAAG,EAAEA;IAAI,EAAE,CAAC;EAClE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAA,EAAe;EACxC,OAAOR,iBAAiB,CAAC,CAAC;AAC5B;;AAEA;AACA;AACA;AACA,OAAO,MAAMS,gBAAgB,GAAG1D,qBAAqB,CAAC2D,QAAQ;;AAE9D;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAG5D,qBAAqB", "ignoreList": []}